class InspectionForm {
    constructor() {
        this.canvas = document.getElementById('drawing-canvas');
        this.ctx = this.canvas.getContext('2d');
        this.isDrawing = false;
        this.currentColor = '#000000';
        this.images = [];
        this.currentSection = 'basic';
        this.formData = this.loadFormData(); // Auto-save functionality
        
        this.initializeCanvas();
        this.initializeEventListeners();
        this.initializeMobileNavigation();
        this.setCanvasSize();
        this.restoreFormData();
    }

    setCanvasSize() {
        const container = this.canvas.parentElement;
        const containerWidth = container.clientWidth - 20;
        this.canvas.width = Math.min(300, containerWidth);
        this.canvas.height = 150;
        
        // Set canvas styling
        this.ctx.lineCap = 'round';
        this.ctx.lineJoin = 'round';
        this.ctx.lineWidth = 2;
        this.ctx.strokeStyle = this.currentColor;
    }

    initializeCanvas() {
        this.setCanvasSize();
        window.addEventListener('resize', () => this.setCanvasSize());
    }

    initializeEventListeners() {
        // Dark mode toggle
        document.getElementById('dark-mode-toggle').addEventListener('click', () => this.toggleDarkMode());

        // GPS location button
        const locationBtn = document.getElementById('get-location');
        if (locationBtn) {
            locationBtn.addEventListener('click', () => this.getGPSLocation());
        }

        // Drawing events
        this.canvas.addEventListener('mousedown', (e) => this.startDrawing(e));
        this.canvas.addEventListener('mousemove', (e) => this.draw(e));
        this.canvas.addEventListener('mouseup', () => this.stopDrawing());
        this.canvas.addEventListener('mouseleave', () => this.stopDrawing());

        // Enhanced touch events with palm rejection
        this.canvas.addEventListener('touchstart', (e) => this.handleTouch(e, 'start'), { passive: false });
        this.canvas.addEventListener('touchmove', (e) => this.handleTouch(e, 'move'), { passive: false });
        this.canvas.addEventListener('touchend', (e) => this.handleTouch(e, 'end'), { passive: false });

        // Control buttons
        document.getElementById('clear-canvas').addEventListener('click', () => this.clearCanvas());

        // File input
        document.getElementById('camera').addEventListener('change', (e) => this.handleImageUpload(e));

        // Form submission
        document.getElementById('inspection-form').addEventListener('submit', (e) => this.generatePDF(e));

        // Auto-save form data
        this.setupAutoSave();

        // Load dark mode preference
        this.loadDarkModePreference();

        // Handle orientation changes
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                this.setCanvasSize();
                this.updateMobileNavigation();
            }, 100);
        });

        // Handle window resize
        window.addEventListener('resize', () => {
            this.setCanvasSize();
        });

        // Prevent zoom on double tap for iOS
        let lastTouchEnd = 0;
        document.addEventListener('touchend', (e) => {
            const now = (new Date()).getTime();
            if (now - lastTouchEnd <= 300) {
                e.preventDefault();
            }
            lastTouchEnd = now;
        }, false);
    }

    initializeMobileNavigation() {
        const navBtns = document.querySelectorAll('.nav-btn');
        const sections = document.querySelectorAll('.form-section');

        // Initialize mobile navigation behavior
        this.updateMobileNavigation();

        // Listen for window resize to toggle mobile/desktop behavior
        window.addEventListener('resize', () => {
            this.updateMobileNavigation();
        });

        navBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const targetSection = btn.dataset.section;
                this.showSection(targetSection);

                // Update active nav button
                navBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
            });
        });

        // Add swipe gestures for form navigation
        this.addSwipeGestures();
    }

    updateMobileNavigation() {
        const sections = document.querySelectorAll('.form-section');
        const isMobile = window.innerWidth <= 768;

        if (isMobile) {
            // Mobile: Show only first section by default, hide others
            sections.forEach((section, index) => {
                if (index === 0) {
                    section.classList.add('active');
                } else {
                    section.classList.remove('active');
                }
            });

            // Ensure first nav button is active
            const firstNavBtn = document.querySelector('.nav-btn[data-section="basic"]');
            if (firstNavBtn) {
                document.querySelectorAll('.nav-btn').forEach(btn => btn.classList.remove('active'));
                firstNavBtn.classList.add('active');
            }
        } else {
            // Desktop: Show all sections
            sections.forEach(section => {
                section.classList.add('active');
            });
        }
    }

    showSection(sectionName) {
        const sections = document.querySelectorAll('.form-section');
        const isMobile = window.innerWidth <= 768;

        sections.forEach(section => {
            if (section.dataset.section === sectionName) {
                section.classList.add('active');
                // Smooth scroll with proper offset for mobile nav
                setTimeout(() => {
                    const offset = isMobile ? 80 : 20; // Account for mobile nav height
                    const elementTop = section.offsetTop - offset;
                    window.scrollTo({
                        top: elementTop,
                        behavior: 'smooth'
                    });
                }, 100);
            } else if (isMobile) {
                // On mobile, hide non-active sections
                section.classList.remove('active');
            }
            // On desktop, keep all sections visible (don't remove active class)
        });
        this.currentSection = sectionName;
    }

    addSwipeGestures() {
        let startX = 0;
        let startY = 0;
        let startTime = 0;

        document.addEventListener('touchstart', (e) => {
            // Only handle swipes on the main container, not on input elements
            if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA' || e.target.tagName === 'CANVAS') {
                return;
            }

            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
            startTime = Date.now();
        }, { passive: true });

        document.addEventListener('touchend', (e) => {
            if (!e.changedTouches[0]) return;

            // Only handle swipes on the main container, not on input elements
            if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA' || e.target.tagName === 'CANVAS') {
                return;
            }

            const endX = e.changedTouches[0].clientX;
            const endY = e.changedTouches[0].clientY;
            const diffX = startX - endX;
            const diffY = startY - endY;
            const timeDiff = Date.now() - startTime;

            // Only trigger if horizontal swipe is more significant than vertical
            // and it's a quick swipe (not a slow drag)
            if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 80 && timeDiff < 500) {
                if (window.innerWidth <= 768) { // Only on mobile
                    e.preventDefault();
                    if (diffX > 0) {
                        this.nextSection();
                    } else {
                        this.previousSection();
                    }
                }
            }
        }, { passive: false });
    }

    nextSection() {
        const sections = ['basic', 'time', 'measurements', 'documentation'];
        const currentIndex = sections.indexOf(this.currentSection);
        if (currentIndex < sections.length - 1) {
            const nextSection = sections[currentIndex + 1];
            this.showSection(nextSection);
            this.updateNavButton(nextSection);
        }
    }

    previousSection() {
        const sections = ['basic', 'time', 'measurements', 'documentation'];
        const currentIndex = sections.indexOf(this.currentSection);
        if (currentIndex > 0) {
            const prevSection = sections[currentIndex - 1];
            this.showSection(prevSection);
            this.updateNavButton(prevSection);
        }
    }

    updateNavButton(sectionName) {
        const navBtns = document.querySelectorAll('.nav-btn');
        navBtns.forEach(btn => {
            if (btn.dataset.section === sectionName) {
                btn.classList.add('active');
            } else {
                btn.classList.remove('active');
            }
        });
    }

    getGPSLocation() {
        const gpsInput = document.getElementById('gps-coordinates');
        const locationBtn = document.getElementById('get-location');

        if (!navigator.geolocation) {
            this.showToast('Geolocation is not supported by this browser.', 'error');
            return;
        }

        locationBtn.textContent = 'Getting...';
        locationBtn.disabled = true;
        locationBtn.classList.add('loading');

        // Add timeout handling for better mobile experience
        const timeoutId = setTimeout(() => {
            this.showToast('Location request timed out. Please try again.', 'error');
            locationBtn.textContent = 'Get GPS';
            locationBtn.disabled = false;
            locationBtn.classList.remove('loading');
        }, 15000);

        navigator.geolocation.getCurrentPosition(
            (position) => {
                clearTimeout(timeoutId);
                const lat = position.coords.latitude.toFixed(6);
                const lon = position.coords.longitude.toFixed(6);
                const accuracy = position.coords.accuracy.toFixed(0);

                gpsInput.value = `${lat}, ${lon}`;
                locationBtn.textContent = 'Get GPS';
                locationBtn.disabled = false;
                locationBtn.classList.remove('loading');

                this.showToast(`Location captured (±${accuracy}m accuracy)`, 'success');

                // Provide haptic feedback if available
                if (navigator.vibrate) {
                    navigator.vibrate(50);
                }

                // Optionally reverse geocode to get address
                this.reverseGeocode(lat, lon);
            },
            (error) => {
                clearTimeout(timeoutId);
                console.error('Geolocation error:', error);

                let errorMessage = 'Unable to get location. ';
                switch(error.code) {
                    case error.PERMISSION_DENIED:
                        errorMessage += 'Location access denied. Please enable location services.';
                        break;
                    case error.POSITION_UNAVAILABLE:
                        errorMessage += 'Location information unavailable.';
                        break;
                    case error.TIMEOUT:
                        errorMessage += 'Location request timed out.';
                        break;
                    default:
                        errorMessage += 'Unknown error occurred.';
                        break;
                }

                this.showToast(errorMessage, 'error');
                locationBtn.textContent = 'Get GPS';
                locationBtn.disabled = false;
                locationBtn.classList.remove('loading');
            },
            {
                enableHighAccuracy: true,
                timeout: 12000,
                maximumAge: 300000 // 5 minutes
            }
        );
    }

    showToast(message, type = 'info') {
        // Create toast notification
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.textContent = message;
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'error' ? '#e74c3c' : type === 'success' ? '#27ae60' : '#3498db'};
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            z-index: 10000;
            font-size: 14px;
            max-width: 300px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            animation: slideInRight 0.3s ease-out;
        `;

        document.body.appendChild(toast);

        // Remove toast after 4 seconds
        setTimeout(() => {
            toast.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => {
                if (document.body.contains(toast)) {
                    document.body.removeChild(toast);
                }
            }, 300);
        }, 4000);
    }

    reverseGeocode(lat, lon) {
        // Optional: Use a geocoding service to get readable address
        // For now, just keep coordinates
        console.log(`Location captured: ${lat}, ${lon}`);
    }

    setupAutoSave() {
        const form = document.getElementById('inspection-form');
        const inputs = form.querySelectorAll('input, textarea, select');
        
        inputs.forEach(input => {
            input.addEventListener('input', () => {
                this.saveFormData();
            });
            input.addEventListener('change', () => {
                this.saveFormData();
            });
        });
        
        // Save every 30 seconds
        setInterval(() => {
            this.saveFormData();
        }, 30000);
    }

    saveFormData() {
        try {
            const formData = new FormData(document.getElementById('inspection-form'));
            const data = Object.fromEntries(formData);
            localStorage.setItem('inspectionFormData', JSON.stringify(data));
        } catch (error) {
            console.warn('Could not save form data:', error);
        }
    }

    loadFormData() {
        try {
            const saved = localStorage.getItem('inspectionFormData');
            return saved ? JSON.parse(saved) : {};
        } catch (error) {
            console.warn('Could not load form data:', error);
            return {};
        }
    }

    restoreFormData() {
        if (Object.keys(this.formData).length === 0) return;
        
        Object.entries(this.formData).forEach(([name, value]) => {
            const input = document.querySelector(`[name="${name}"]`);
            if (input && value) {
                input.value = value;
            }
        });
    }

    startDrawing(e) {
        this.isDrawing = true;
        this.ctx.beginPath();
        this.ctx.moveTo(e.offsetX, e.offsetY);
    }

    draw(e) {
        if (!this.isDrawing) return;
        this.ctx.lineTo(e.offsetX, e.offsetY);
        this.ctx.stroke();
    }

    stopDrawing() {
        this.isDrawing = false;
        this.ctx.beginPath();
    }

    handleTouch(e, action) {
        e.preventDefault();

        // Enhanced palm rejection - ignore touches with multiple fingers for drawing
        if (action === 'move' && e.touches.length > 1) {
            return;
        }

        // Ignore touches that are too large (likely palm)
        if (e.touches && e.touches[0] && e.touches[0].radiusX > 20) {
            return;
        }

        const touch = e.touches[0] || e.changedTouches[0];
        const rect = this.canvas.getBoundingClientRect();
        const scaleX = this.canvas.width / rect.width;
        const scaleY = this.canvas.height / rect.height;

        const offsetX = (touch.clientX - rect.left) * scaleX;
        const offsetY = (touch.clientY - rect.top) * scaleY;

        switch(action) {
            case 'start':
                this.startDrawing({offsetX, offsetY});
                // Provide haptic feedback if available
                if (navigator.vibrate) {
                    navigator.vibrate(10);
                }
                break;
            case 'move':
                if (e.touches.length === 1) {
                    this.draw({offsetX, offsetY});
                }
                break;
            case 'end':
                this.stopDrawing();
                // Light haptic feedback on end
                if (navigator.vibrate) {
                    navigator.vibrate(5);
                }
                break;
        }
    }

    clearCanvas() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
    }

    toggleDarkMode() {
        document.body.classList.toggle('dark-mode');
        const isDarkMode = document.body.classList.contains('dark-mode');
        const button = document.getElementById('dark-mode-toggle');
        
        if (isDarkMode) {
            button.innerHTML = '☀️ Light Mode';
        } else {
            button.innerHTML = '🌙 Dark Mode';
        }
        
        // Save preference
        localStorage.setItem('darkMode', isDarkMode);
    }

    loadDarkModePreference() {
        const isDarkMode = localStorage.getItem('darkMode') === 'true';
        if (isDarkMode) {
            document.body.classList.add('dark-mode');
            document.getElementById('dark-mode-toggle').innerHTML = '☀️ Light Mode';
        }
    }

    handleImageUpload(e) {
        const files = Array.from(e.target.files);
        const preview = document.getElementById('image-preview');
        
        files.forEach(file => {
            // Compress image for better performance on mobile
            this.compressImage(file, (compressedFile) => {
                const reader = new FileReader();
                reader.onload = (event) => {
                    const img = document.createElement('img');
                    img.src = event.target.result;
                    img.style.maxWidth = '150px';
                    img.style.maxHeight = '150px';
                    img.style.margin = '5px';
                    img.style.border = '1px solid #ddd';
                    img.style.borderRadius = '8px';
                    img.style.cursor = 'pointer';
                    
                    // Add click to preview full size
                    img.addEventListener('click', () => {
                        this.showImagePreview(event.target.result);
                    });
                    
                    preview.appendChild(img);
                    this.images.push(event.target.result);
                    this.saveFormData(); // Auto-save when images are added
                };
                reader.readAsDataURL(compressedFile);
            });
        });
    }

    compressImage(file, callback) {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        const img = new Image();
        
        img.onload = function() {
            // Set max dimensions
            const maxWidth = 1024;
            const maxHeight = 1024;
            let { width, height } = img;
            
            // Calculate new dimensions
            if (width > maxWidth || height > maxHeight) {
                const ratio = Math.min(maxWidth / width, maxHeight / height);
                width *= ratio;
                height *= ratio;
            }
            
            canvas.width = width;
            canvas.height = height;
            
            // Draw and compress
            ctx.drawImage(img, 0, 0, width, height);
            canvas.toBlob(callback, 'image/jpeg', 0.8);
        };
        
        img.src = URL.createObjectURL(file);
    }

    showImagePreview(imageSrc) {
        // Create modal for full-size image preview
        const modal = document.createElement('div');
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.9);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            cursor: pointer;
        `;
        
        const img = document.createElement('img');
        img.src = imageSrc;
        img.style.cssText = `
            max-width: 90%;
            max-height: 90%;
            border-radius: 8px;
        `;
        
        modal.appendChild(img);
        document.body.appendChild(modal);
        
        modal.addEventListener('click', () => {
            document.body.removeChild(modal);
        });
    }

    validateForm() {
        const requiredFields = document.querySelectorAll('[required]');
        let isValid = true;
        let firstInvalidField = null;
        
        requiredFields.forEach(field => {
            const value = field.value.trim();
            const isFieldValid = value !== '';
            
            if (!isFieldValid) {
                field.style.borderColor = '#e74c3c';
                field.classList.add('error');
                isValid = false;
                
                if (!firstInvalidField) {
                    firstInvalidField = field;
                }
                
                // Add shake animation
                field.style.animation = 'shake 0.5s ease-in-out';
                setTimeout(() => {
                    field.style.animation = '';
                }, 500);
            } else {
                field.style.borderColor = '#27ae60';
                field.classList.remove('error');
                field.classList.add('success');
            }
        });
        
        // Scroll to first invalid field and show its section
        if (firstInvalidField) {
            const section = firstInvalidField.closest('.form-section');
            if (section && window.innerWidth <= 768) {
                const sectionName = section.dataset.section;
                this.showSection(sectionName);
                this.updateNavButton(sectionName);
            }
            
            setTimeout(() => {
                firstInvalidField.focus();
                firstInvalidField.scrollIntoView({ 
                    behavior: 'smooth', 
                    block: 'center' 
                });
            }, 300);
        }
        
        return isValid;
    }

    async generatePDF(event) {
        event.preventDefault();
        
        if (!this.validateForm()) {
            alert('Please fill in all required fields.');
            return;
        }

        try {
            // Show loading state
            const submitBtn = event.target.querySelector('[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = 'Generating PDF...';
            submitBtn.disabled = true;

            // Get form data
            const formData = new FormData(document.getElementById('inspection-form'));
            const data = Object.fromEntries(formData);

            // Create professional PDF report
            const pdf = new jsPDF({
                orientation: 'portrait',
                unit: 'mm',
                format: 'a4'
            });

            console.log('Starting PDF generation...');

            // Add company header with logo
            try {
                await this.addPDFHeader(pdf);
                console.log('Header added successfully');
            } catch (error) {
                console.warn('Header error:', error);
                this.addSimpleHeader(pdf);
            }
            
            // Add form data sections with compact spacing
            try {
                let yPos = this.addBasicInformation(pdf, data, 42);
                console.log('Basic info added, yPos:', yPos);
                
                yPos = this.addTimeEnvironmental(pdf, data, yPos + 12);
                console.log('Time/env added, yPos:', yPos);
                
                yPos = this.addMeasurements(pdf, data, yPos + 12);
                console.log('Measurements added, yPos:', yPos);
                
                yPos = this.addDocumentation(pdf, data, yPos + 12);
                console.log('Documentation added, yPos:', yPos);
                
                // Add signature and images in remaining space
                if (this.hasCanvasContent() || this.images.length > 0) {
                    yPos = this.addSignatureAndImages(pdf, yPos + 8);
                    console.log('Signature/images added, yPos:', yPos);
                }
            } catch (error) {
                console.error('Content generation error:', error);
                throw error;
            }

            // Add footer
            this.addPDFFooter(pdf);
            console.log('Footer added');

            // Generate filename with timestamp
            const now = new Date();
            const timestamp = now.toISOString().slice(0, 19).replace(/[:.]/g, '-');
            const location = (data.location || 'Unknown-Location').replace(/[^a-zA-Z0-9]/g, '-');
            const assetId = (data['asset-id'] || 'Unknown-Asset-ID').replace(/[^a-zA-Z0-9]/g, '-');
            const filename = `Wind-Farm-Inspection-${location}-${assetId}-${timestamp}.pdf`;
            
            pdf.save(filename);
            console.log('PDF saved successfully:', filename);

            // Reset button
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;

        } catch (error) {
            console.error('Error generating PDF:', error);
            alert(`Error generating PDF: ${error.message}. Please check the console for more details.`);
            
            // Reset button on error
            const submitBtn = event.target.querySelector('[type="submit"]');
            submitBtn.textContent = 'Generate PDF Report';
            submitBtn.disabled = false;
        }
    }

    addSimpleHeader(pdf) {
        // Fallback header without logo
        pdf.setFillColor(30, 60, 114);
        pdf.rect(0, 0, 210, 35, 'F');
        
        pdf.setTextColor(255, 255, 255);
        pdf.setFontSize(20);
        pdf.setFont('helvetica', 'bold');
        pdf.text('WIND FARM INSPECTION REPORT', 15, 17);
        
        pdf.setFontSize(10);
        pdf.setFont('helvetica', 'normal');
        const now = new Date();
        pdf.text(`Generated: ${now.toLocaleDateString()} ${now.toLocaleTimeString()}`, 15, 25);
        
        pdf.setTextColor(0, 0, 0);
    }

    async addPDFHeader(pdf) {
        // Company header background
        pdf.setFillColor(30, 60, 114); // Professional blue
        pdf.rect(0, 0, 210, 35, 'F');
        
        try {
            // Load and add logo
            const logoImg = await this.loadImage('./Logo.png');
            pdf.addImage(logoImg, 'PNG', 15, 7, 25, 20); // x, y, width, height
            
            // Company text next to logo
            pdf.setTextColor(255, 255, 255);
            pdf.setFontSize(20);
            pdf.setFont('helvetica', 'bold');
            pdf.text('WIND FARM INSPECTION REPORT', 45, 17);
            
            // Current date
            pdf.setFontSize(10);
            pdf.setFont('helvetica', 'normal');
            const now = new Date();
            pdf.text(`Generated: ${now.toLocaleDateString()} ${now.toLocaleTimeString()}`, 45, 25);
            
        } catch (error) {
            console.warn('Could not load logo, using text only header:', error);
            // Fallback to text-only header
            pdf.setTextColor(255, 255, 255);
            pdf.setFontSize(20);
            pdf.setFont('helvetica', 'bold');
            pdf.text('WIND FARM INSPECTION REPORT', 15, 17);
            
            // Current date
            pdf.setFontSize(10);
            pdf.setFont('helvetica', 'normal');
            const now = new Date();
            pdf.text(`Generated: ${now.toLocaleDateString()} ${now.toLocaleTimeString()}`, 15, 25);
        }
        
        // Reset text color for rest of document
        pdf.setTextColor(0, 0, 0);
    }

    loadImage(src) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.crossOrigin = 'anonymous';
            img.onload = () => {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                canvas.width = img.width;
                canvas.height = img.height;
                ctx.drawImage(img, 0, 0);
                resolve(canvas.toDataURL('image/png'));
            };
            img.onerror = reject;
            img.src = src;
        });
    }

    addBasicInformation(pdf, data, yPos) {
        pdf.setFontSize(12);
        pdf.setFont('helvetica', 'bold');
        pdf.text('Basic Information', 15, yPos);
        
        pdf.setFontSize(9);
        pdf.setFont('helvetica', 'normal');
        yPos += 6;
        
        pdf.text(`Location: ${data.location || 'N/A'}`, 15, yPos);
        pdf.text(`Asset Type: ${data['asset-type'] || 'N/A'}`, 105, yPos);
        yPos += 5;
        
        pdf.text(`Asset ID: ${data['asset-id'] || 'N/A'}`, 15, yPos);
        pdf.text(`GPS Coordinates: ${data['gps-coordinates'] || 'N/A'}`, 105, yPos);
        
        return yPos;
    }

    addTimeEnvironmental(pdf, data, yPos) {
        pdf.setFontSize(12);
        pdf.setFont('helvetica', 'bold');
        pdf.text('Time & Environmental Conditions', 15, yPos);
        
        pdf.setFontSize(9);
        pdf.setFont('helvetica', 'normal');
        yPos += 6;
        
        const startTime = data['start-time'] ? new Date(data['start-time']).toLocaleString() : 'N/A';
        const finishTime = data['finish-time'] ? new Date(data['finish-time']).toLocaleString() : 'N/A';
        
        pdf.text(`Start: ${startTime}`, 15, yPos);
        yPos += 4;
        pdf.text(`Finish: ${finishTime}`, 15, yPos);
        yPos += 6;
        
        pdf.text(`Ambient Temp: ${data['ambient-temp'] || 'N/A'}°C`, 15, yPos);
        pdf.text(`Wind Speed: ${data['wind-speed'] || 'N/A'} m/s`, 105, yPos);
        yPos += 4;
        pdf.text(`Humidity: ${data.humidity || 'N/A'}%`, 15, yPos);
        
        return yPos;
    }

    addMeasurements(pdf, data, yPos) {
        pdf.setFontSize(12);
        pdf.setFont('helvetica', 'bold');
        pdf.text('Measurements', 15, yPos);
        
        pdf.setFontSize(9);
        pdf.setFont('helvetica', 'normal');
        yPos += 6;
        
        pdf.text(`Power Reading: ${data['power-reading'] || 'N/A'} MW`, 15, yPos);
        pdf.text(`Hotspot Temp: ${data.hotspot || 'N/A'}°C`, 105, yPos);
        yPos += 4;
        pdf.text(`Distance to Target: ${data.distance || 'N/A'} m`, 15, yPos);
        
        return yPos;
    }

    addDocumentation(pdf, data, yPos) {
        pdf.setFontSize(12);
        pdf.setFont('helvetica', 'bold');
        pdf.text('Notes', 15, yPos);
        
        pdf.setFontSize(9);
        pdf.setFont('helvetica', 'normal');
        yPos += 6;
        
        if (data.notes) {
            const notes = data.notes;
            const lines = pdf.splitTextToSize(notes, 180);
            // Limit notes to maximum 3 lines to save space
            const limitedLines = lines.slice(0, 3);
            pdf.text(limitedLines, 15, yPos);
            yPos += limitedLines.length * 4;
        } else {
            pdf.text('No notes provided.', 15, yPos);
            yPos += 4;
        }
        
        return yPos;
    }

    addSignatureAndImages(pdf, yPos) {
        const remainingSpace = 280 - yPos; // Space left until footer
        
        // Create two columns: signature on left, images on right
        let leftColumn = 15;
        let rightColumn = 110;
        let currentY = yPos;
        let imageY = yPos; // Initialize imageY
        
        // Add signature if present (left column)
        if (this.hasCanvasContent()) {
            pdf.setFontSize(10);
            pdf.setFont('helvetica', 'bold');
            pdf.text('Inspector: Trevor', leftColumn, currentY);
            currentY += 6;
            
            pdf.text('Signature:', leftColumn, currentY);
            currentY += 5;
            
            // Smaller signature area
            const canvasDataUrl = this.canvas.toDataURL('image/png');
            pdf.addImage(canvasDataUrl, 'PNG', leftColumn, currentY, 40, 20);
            currentY += 25; // Add space after signature
        }
        
        // Add images if present (right column)
        if (this.images.length > 0) {
            pdf.setFontSize(10);
            pdf.setFont('helvetica', 'bold');
            pdf.text('Images:', rightColumn, yPos);
            
            imageY = yPos + 5;
            let imageCount = 0;
            
            for (const imageData of this.images) {
                if (imageY + 30 > 275) break; // Don't exceed page boundary
                
                try {
                    // Smaller images arranged vertically
                    pdf.addImage(imageData, 'JPEG', rightColumn, imageY, 35, 25);
                    imageY += 28;
                    imageCount++;
                    
                    if (imageCount >= 3) break; // Maximum 3 images to fit on page
                } catch (error) {
                    console.warn('Failed to add image to PDF:', error);
                }
            }
        }
        
        return Math.max(currentY, imageY);
    }

    hasCanvasContent() {
        const imageData = this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height);
        const pixels = imageData.data;
        
        for (let i = 0; i < pixels.length; i += 4) {
            if (pixels[i + 3] !== 0) { // Check alpha channel
                return true;
            }
        }
        return false;
    }

    addPDFFooter(pdf) {
        const pageCount = pdf.internal.getNumberOfPages();
        
        for (let i = 1; i <= pageCount; i++) {
            pdf.setPage(i);
            
            // Footer line
            pdf.setDrawColor(30, 60, 114);
            pdf.line(15, 285, 195, 285);
            
            // Footer text
            pdf.setFontSize(8);
            pdf.setTextColor(100, 100, 100);
            pdf.text('Wind Farm Inspection Report', 15, 290);
            pdf.text(`Page ${i} of ${pageCount}`, 180, 290);
            
            // Company info
            pdf.text('Generated by Professional Inspection System', 15, 294);
        }
    }
}

// Initialize the form when the page loads
window.addEventListener('DOMContentLoaded', () => {
    window.jsPDF = window.jspdf.jsPDF;
    new InspectionForm();
    
    // Register service worker for offline support
    if ('serviceWorker' in navigator) {
        navigator.serviceWorker.register('/sw.js')
            .then(registration => {
                console.log('Service Worker registered successfully:', registration);
            })
            .catch(error => {
                console.log('Service Worker registration failed:', error);
            });
    }
});